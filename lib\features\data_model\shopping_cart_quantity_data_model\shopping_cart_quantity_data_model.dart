import 'package:flutter/material.dart';

class ShoppingCartQuantityDataModel with ChangeNotifier {
  List<String> productReferenceList2 = [];
  List<String> productReferenceList = []; // New list to track product+variant combinations


  // List<String> get updatedCartQuantity => productReferenceList;
  List<String> get updatedCartItemIdentifiers => productReferenceList;


  ShoppingCartQuantityDataModel(){
    //print("Hello");
  }

  void updateCartQuantity({required String productReference, String? variantReference}) {
    // productReferenceList.add(productReference);// Assign the parameter to cartQuantity

    // Create unique identifier for product+variant combination
    String cartItemIdentifier = _createCartItemIdentifier(productReference, variantReference?? '');
    productReferenceList.add(cartItemIdentifier);

    notifyListeners();
  }

  // Helper method to create unique identifier for cart items
  String _createCartItemIdentifier(String productReference, String variantReference) {
    if (variantReference.isNotEmpty) {
      return "${productReference}__${variantReference}";
    }
    return productReference;
  }

  // Check if a specific product+variant combination is in cart
  bool isProductVariantInCart(String productReference, String variantReference) {
    String identifier = _createCartItemIdentifier(productReference, variantReference);
    return productReferenceList.contains(identifier);
  }

  // // Check if any variant of a product is in cart (for backward compatibility)
  // bool isProductInCart(String productReference, String variantReference) {
  //   String identifier = _createCartItemIdentifier(productReference, variantReference??'');
  //   return cartItemIdentifierList.contains(identifier);
  // }

  //region Clear
void clearDara(){
  productReferenceList.clear();
  notifyListeners();

}
//endregion
}
