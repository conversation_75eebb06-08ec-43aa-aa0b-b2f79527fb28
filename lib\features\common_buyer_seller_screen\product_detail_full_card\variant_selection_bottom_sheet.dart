import 'package:flutter/material.dart';
import 'package:swadesic/features/common_buyer_seller_screen/product_detail_full_card/product_detail_full_card_bloc.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class VariantSelectionBottomSheet extends StatefulWidget {
  final Product product;
  final ProductDetailFullCardBloc productDetailFullCardBloc;
  final Function(ProductVariant) onVariantSelected;

  const VariantSelectionBottomSheet({
    Key? key,
    required this.product,
    required this.productDetailFullCardBloc,
    required this.onVariantSelected,
  }) : super(key: key);

  @override
  State<VariantSelectionBottomSheet> createState() => _VariantSelectionBottomSheetState();
}

class _VariantSelectionBottomSheetState extends State<VariantSelectionBottomSheet> {
  ProductVariant? selectedVariant;
  Map<String, String> selectedOptions = {};

  @override
  void initState() {
    super.initState();
    selectedVariant = widget.productDetailFullCardBloc.selectedVariant;
    if (selectedVariant != null) {
      selectedOptions = Map.from(selectedVariant!.combinations);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            height: 4,
            width: 40,
            decoration: BoxDecoration(
              color: AppColors.borderColor1,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Price information or availability message
                _buildPriceSection(),
                const SizedBox(height: 16),
                Text(
                  "Select product options",
                  style: AppTextStyle.access0(textColor: AppColors.appBlack),
                ),
              ],
            ),
          ),
          
          // Options selection
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: _buildOptionSelectors(),
              ),
            ),
          ),
          
          // Done button
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            child: ElevatedButton(
              onPressed: _canSelectVariant()
                ? () {
                    if (selectedVariant != null) {
                      widget.onVariantSelected(selectedVariant!);
                    }
                    Navigator.pop(context);
                  }
                : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.appBlack,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                "Done",
                style: AppTextStyle.contentHeading0(textColor: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSection() {
    // Check if all options are selected
    bool allOptionsSelected = widget.product.options != null &&
        widget.product.options!.keys.every((optionName) =>
            selectedOptions.containsKey(optionName));

    if (!allOptionsSelected) {
      // Show default message when not all options are selected
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Selling price",
            style: AppTextStyle.smallText(textColor: AppColors.appBlack),
          ),
          const SizedBox(height: 4),
          Text(
            "Select all options to see price",
            style: AppTextStyle.contentText0(textColor: AppColors.writingBlack1),
          ),
        ],
      );
    }

    if (selectedVariant == null) {
      // Show "combination not available" message
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Selling price",
            style: AppTextStyle.smallText(textColor: AppColors.appBlack),
          ),
          const SizedBox(height: 4),
          Text(
            "Combination is not available yet",
            style: AppTextStyle.contentText0(textColor: AppColors.red),
          ),
        ],
      );
    }

    // Show actual variant pricing
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Selling price",
          style: AppTextStyle.access0(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Text(
              "₹${_getSelectedVariantSellingPrice()}",
              style: AppTextStyle.pageHeading(textColor: AppColors.appBlack),
            ),
            const SizedBox(width: 8),
            if (_getSelectedVariantMrpPrice() != _getSelectedVariantSellingPrice())
              Text(
                "₹${_getSelectedVariantMrpPrice()}",
                style: AppTextStyle.access0Strike(
                  textColor: AppColors.writingBlack1,
                ),
              ),
            const SizedBox(width: 8),
            if (_getSelectedVariantMrpPrice() != _getSelectedVariantSellingPrice())
              Text(
                "${_getDiscountPercentage()}% OFF",
                style: AppTextStyle.access0(textColor: AppColors.brandBlack),
              ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          _getSelectedVariantStock() > 0
            ? "Only ${_getSelectedVariantStock()} available"
            : "Out of stock",
          style: AppTextStyle.smallText(
            textColor: _getSelectedVariantStock() > 0
              ? AppColors.orange
              : AppColors.red,
          ),
        ),
      ],
    );
  }

  List<Widget> _buildOptionSelectors() {
    List<Widget> widgets = [];
    
    if (widget.product.options != null) {
      widget.product.options!.forEach((optionName, optionValues) {
        widgets.add(_buildOptionSelector(optionName, optionValues));
        widgets.add(const SizedBox(height: 16));
      });
    }
    
    return widgets;
  }

  Widget _buildOptionSelector(String optionName, List<dynamic> optionValues) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          optionName,
          style: AppTextStyle.smallText(textColor: AppColors.appBlack),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: optionValues.map((value) {
            final stringValue = value.toString();
            final isSelected = selectedOptions[optionName] == stringValue;
            final isAvailable = _isOptionValueAvailable(optionName, stringValue);
            
            return GestureDetector(
              onTap: () => _selectOption(optionName, stringValue),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                    ? AppColors.appBlack
                    : Colors.white,
                  border: Border.all(
                    color: isSelected
                      ? AppColors.appBlack
                      : AppColors.borderColor1,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Opacity(
                  opacity: isAvailable ? 1.0 : 0.5,
                  child: Text(
                    stringValue,
                    style: AppTextStyle.smallText(
                      textColor: isSelected ? Colors.white : AppColors.appBlack,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _selectOption(String optionName, String optionValue) {
    setState(() {
      selectedOptions[optionName] = optionValue;
      _updateSelectedVariant();
    });
  }

  void _updateSelectedVariant() {
    final variants = widget.productDetailFullCardBloc.getVariantsWithCombinations();
    
    for (final variant in variants) {
      bool matches = true;
      for (final entry in selectedOptions.entries) {
        if (variant.combinations[entry.key] != entry.value) {
          matches = false;
          break;
        }
      }
      if (matches && variant.combinations.length == selectedOptions.length) {
        selectedVariant = variant;
        return;
      }
    }
    selectedVariant = null;
  }

  bool _isOptionValueAvailable(String optionName, String optionValue) {
    final variants = widget.productDetailFullCardBloc.getVariantsWithCombinations();

    // Create a temporary selection with this option value
    Map<String, String> tempSelection = Map.from(selectedOptions);
    tempSelection[optionName] = optionValue;

    // Check if any variant matches this selection (partially or fully) and has stock
    for (final variant in variants) {
      bool matches = true;
      for (final entry in tempSelection.entries) {
        if (variant.combinations[entry.key] != entry.value) {
          matches = false;
          break;
        }
      }
      if (matches && variant.stock > 0) {
        return true;
      }
    }

    // If no variant with stock found, still return true to allow selection
    // The opacity will be reduced to show it's not available with stock
    return false;
  }

  int _getSelectedVariantSellingPrice() {
    return selectedVariant?.sellingPrice ?? widget.productDetailFullCardBloc.getDisplaySellingPrice();
  }

  int _getSelectedVariantMrpPrice() {
    return selectedVariant?.mrpPrice ?? widget.productDetailFullCardBloc.getDisplayMrpPrice();
  }

  int _getSelectedVariantStock() {
    return selectedVariant?.stock ?? widget.productDetailFullCardBloc.getDisplayStock();
  }

  int _getDiscountPercentage() {
    final mrp = _getSelectedVariantMrpPrice();
    final selling = _getSelectedVariantSellingPrice();

    if (mrp <= 0 || selling < 0) {
      return 0;
    }

    return (((mrp - selling) / mrp) * 100).round();
  }

  bool _canSelectVariant() {
    // Check if all options are selected
    if (widget.product.options != null) {
      for (String optionName in widget.product.options!.keys) {
        if (!selectedOptions.containsKey(optionName)) {
          return false; // Not all options selected
        }
      }
    }

    // Allow selection even if variant doesn't exist or is out of stock
    // The UI will show appropriate messages
    return true;
  }
}
